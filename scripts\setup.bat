@echo off
echo Setting up Cursor Talk to Figma MCP...

REM Create .cursor directory if it doesn't exist
if not exist ".cursor" mkdir .cursor

REM Install dependencies
echo Installing dependencies...
bun install

REM Create mcp.json with the current directory path
echo Creating MCP configuration...
(
echo {
echo   "mcpServers": {
echo     "TalkToFigma": {
echo       "command": "bunx",
echo       "args": [
echo         "cursor-talk-to-figma-mcp@latest"
echo       ]
echo     }
echo   }
echo }
) > .cursor\mcp.json

echo Setup completed successfully!
echo You can now use the MCP server with Cursor.
