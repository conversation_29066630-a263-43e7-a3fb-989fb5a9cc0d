{"name": "talk-to-figma-mcp", "version": "1.0.0", "description": "MCP server for Figma integration", "main": "server.ts", "type": "module", "scripts": {"start": "node --loader ts-node/esm server.ts", "build": "tsc", "dev": "node --loader ts-node/esm --watch server.ts"}, "keywords": ["figma", "mcp", "cursor", "ai"], "dependencies": {"@modelcontextprotocol/sdk": "^1.4.0", "uuid": "^9.0.1", "ws": "^8.16.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.5", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}